const crypto = require('crypto');

const ordinary = {
    captureMoMoWallet: ['accessKey', 'amount', 'extraData', 'ipnUrl', 'orderId', 'orderInfo', 'partnerCode', 'redirectUrl', 'requestId', 'requestType'],
    transactionStatus: ['partnerCode', 'accessKey', 'requestId', 'orderId', 'requestType']
}

const sort_keys = (data, options) => {
    const itemList = ordinary[options];
    //maps and join
    const resultList = itemList.map(item => {
        let a = `${item}=${data[item]}`;
        return a;
    })
    return resultList.join("&");
}

const get_data_signature = (data, options, secretKey) => {
    let rawSignature = sort_keys(data, options);
    console.log('Node.js Raw signature string:', rawSignature);
    let signature = crypto.createHmac('sha256', secretKey)
        .update(rawSignature)
        .digest('hex');
    console.log('Node.js Generated signature:', signature);
    return signature
}

// Test data that matches Go implementation
let data = {
    partnerCode: 'MOMO123',
    accessKey: 'test_access_key',
    requestId: 'test_request_123',
    amount: 100000,
    orderId: 'test_request_123',
    orderInfo: 'Thanh toán momo cho order test_order',
    redirectUrl: 'https://example.com/callback',
    ipnUrl: 'https://example.com/ipn',
    requestType: 'captureWallet',
    extraData: '',
    lang: 'vi',
}

let secretKey = 'test_secret_key';

console.log('Testing MOMO signature generation with Node.js implementation:');
let signature = get_data_signature(data, "captureMoMoWallet", secretKey);
