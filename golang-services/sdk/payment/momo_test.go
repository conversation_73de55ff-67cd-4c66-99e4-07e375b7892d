package payment

import (
	"testing"
)

// TestMomoVerifyCredentials is commented out as VerifyCredentials method is not implemented
// func TestMomoVerifyCredentials(t *testing.T) {
// 	client := NewMomoClient()
// 	// Implementation would require VerifyCredentials method
// }

func TestMomoClient(t *testing.T) {
	client := NewMomoClient()

	if client == nil {
		t.Fatal("Failed to create MoMo client")
	}

	if client.GetProviderName() != "momo" {
		t.<PERSON>("Expected provider name 'momo', got '%s'", client.GetProviderName())
	}

	if client.BaseURL != "https://payment.momo.vn" {
		t.<PERSON><PERSON><PERSON>("Expected base URL 'https://payment.momo.vn', got '%s'", client.BaseURL)
	}
}

func TestMomoSignatureGeneration(t *testing.T) {
	client := NewMomoClient()

	// Test data that matches Node.js implementation
	req := MomoRequest{
		PartnerCode: "MOMO123",
		AccessKey:   "test_access_key",
		RequestID:   "test_request_123",
		Amount:      100000,
		OrderID:     "test_request_123",
		OrderInfo:   "Thanh toán momo cho order test_order",
		RedirectURL: "https://example.com/callback",
		IpnURL:      "https://example.com/ipn",
		RequestType: "captureWallet",
		ExtraData:   "",
		Lang:        "vi",
	}

	secretKey := "test_secret_key"

	// Generate signature
	signature := client.generateSignature(req, "captureMoMoWallet", secretKey)

	t.Logf("Generated signature: %s", signature)

	// The signature should be consistent
	signature2 := client.generateSignature(req, "captureMoMoWallet", secretKey)
	if signature != signature2 {
		t.Errorf("Signature generation is not consistent: %s != %s", signature, signature2)
	}
}
